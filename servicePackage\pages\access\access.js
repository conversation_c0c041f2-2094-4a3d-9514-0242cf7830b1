// pages/access/access.js
const util = require('@/utils/util.js')

Page({
  data: {
    darkMode: false,
    accessCards: [],
    visitors: [],
    activeTab: 'cards'
  },

  onLoad: function() {
    // 加载智慧通行数据
    this.loadAccessCards()
    this.loadVisitors()
  },

  onShow: function() {
    // 检查暗黑模式
    const app = getApp()
    if (app.globalData && app.globalData.darkMode !== undefined) {
      this.setData({
        darkMode: app.globalData.darkMode
      })
    }
  },

  // 切换标签页
  switchTab: function(e) {
    const tab = e.currentTarget.dataset.tab
    this.setData({
      activeTab: tab
    })
  },

  // 加载门禁卡数据
  loadAccessCards: function() {
    // 模拟数据
    this.setData({
      accessCards: [
        {
          id: 1,
          name: '主卡',
          cardNumber: '8800123456',
          status: 'active',
          lastUsed: '2023-09-15 08:32'
        },
        {
          id: 2,
          name: '家人卡',
          cardNumber: '8800123457',
          status: 'active',
          lastUsed: '2023-09-14 19:45'
        },
        {
          id: 3,
          name: '临时卡',
          cardNumber: '8800123458',
          status: 'inactive',
          lastUsed: '2023-08-30 10:15'
        }
      ]
    })
  },

  // 加载访客记录
  loadVisitors: function() {
    // 模拟数据
    this.setData({
      visitors: [
        {
          id: 1,
          name: '张先生',
          phone: '138****1234',
          visitTime: '2023-09-15 14:00',
          status: 'upcoming',
          qrCode: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAIQAAACECAYAAABRRIOnAAAAAklEQVR4AewaftIAAAOPSURBVO3BQY4cSRLAQDLQ//8yV0c/JZCoain2xjYz/mGMyzHG5RjjcoxxtcNDKW9UOlPJTaUzlTdUnjjGuBxjXI4xrnZ4g0puKk9UclPJTSU3ldxU3lDJTeWJY4zLMcblGONqhy+p5A2V3FRyU8lNJTeVnFRyU8lN5YljjMsxxuUY42qHH1bJTSU3lZxUclM5qeSJY4zLMcblGONqh/+YSm4qb6jkppKbSm4qT6jkppKbyhPHGJdjjMsxxtUOP0wlJ5WcVHJTyU0lN5WcVHJTyUnlG8cYl2OMyzHG1Q5fUslJJTeVnFRyU8lNJSeVnFRyUjmp5KaSm8o3jjEuxxiXY4yrHR5SeUMlN5WcVHJTyU0lN5WcVHJTyUnlppKbyhPHGJdjjMsxxtUO/2MquankpPLEMcblGONyjHG1w5dUclLJSSU3lZxUclLJTSUnlZxUclLJTSU3lZxUclLJSeWJY4zLMcblGONqhzdUclLJTSU3lZxUclPJTSU3lZxUclPJTSUnlZxUclPJSeUbxxiXY4zLMcbVDg+p5KaSk0puKrmp5KaSm0puKrmp5KaSm0puKrmp5KaSm0puKk8cY1yOMS7HGFc7PKSSm0pOKrmp5KaSk0puKjmp5KaSk0puKrmp5KaSm0puKt84xrgcY1yOMa52+GGVnFRyU8lJJTeVnFRyU8lNJTeVnFRyU8lJ5aSSm0pOKk8cY1yOMS7HGFc7fEklJ5WcVHJTyU0lN5WcVHJSyU0lJ5WcVHJTyUklN5WcVJ44xrgcY1yOMa52eEjlDZXcVHJTyU0lN5WcVHJTyUklN5WcVHJTyUklN5UnJP5hjMsxxuUY42qHh1TeUMlNJTeVnFRyU8lNJTeVnFRyU8lNJTeVnFRyU8lJ5YljjMsxxuUY42qHN6jkppKbSk4qOankppKbSk4qOankppKbSk4quankpPKNY4zLMcblGONqhy+p5KaSk0puKrmp5KaSk0puKrmp5KaSm0puKrmp5KaSk8oTxxiXY4zLMcbVDj+skptKbio5qeSmkptKTiq5qeSmkpPKTSU3lW8cY1yOMS7HGFc7/LBKbiq5qeSmkptKbiq5qeSmkpNKbiq5qeSmkpPKE8cYl2OMyzHG1Q5fUslNJTeVnFRyU8lNJTeVnFRyUslNJTeVnFRyU8lJ5RvHGJdjjMsxxtUOD6m8oZKbSm4qOankppKbSm4quankpJKbSm4qOak8cYxxOca4HGP8wxiXY4zLMcblGOP6P0l2/EYcFl3pAAAAAElFTkSuQmCC'
        },
        {
          id: 2,
          name: '李女士',
          phone: '139****5678',
          visitTime: '2023-09-10 16:30',
          status: 'completed'
        },
        {
          id: 3,
          name: '王先生',
          phone: '137****9012',
          visitTime: '2023-09-05 10:00',
          status: 'completed'
        }
      ]
    })
  },

  // 添加门禁卡
  addAccessCard: function() {
    wx.navigateTo({
      url: '/pages/access/add-card'
    })
  },

  // 添加访客
  addVisitor: function() {
    wx.navigateTo({
      url: '/pages/access/add-visitor'
    })
  },

  // 查看门禁卡详情
  viewCardDetail: function(e) {
    const id = e.currentTarget.dataset.id
    debugger
    wx.navigateTo({
      url: `/servicePackage/pages/access/card-detail?id=${id}`
    })
  },

  // 查看访客详情
  viewVisitorDetail: function(e) {
    const id = e.currentTarget.dataset.id
    wx.navigateTo({
      url: `/servicePackage/pages/access/visitor-detail?id=${id}`
    })
  },

  // 显示访客二维码
  showVisitorQRCode: function(e) {
    const id = e.currentTarget.dataset.id
    const visitor = this.data.visitors.find(v => v.id === id)
    
    if (visitor && visitor.qrCode) {
      wx.previewImage({
        urls: [visitor.qrCode],
        current: visitor.qrCode
      })
    } else {
      wx.showToast({
        title: '二维码不可用',
        icon: 'none'
      })
    }
  },

  // 启用/禁用门禁卡
  toggleCardStatus: function(e) {
    const id = e.currentTarget.dataset.id
    const cards = this.data.accessCards.map(card => {
      if (card.id === id) {
        return {
          ...card,
          status: card.status === 'active' ? 'inactive' : 'active'
        }
      }
      return card
    })
    
    this.setData({
      accessCards: cards
    })
    
    wx.showToast({
      title: cards.find(c => c.id === id).status === 'active' ? '已启用' : '已禁用',
      icon: 'success'
    })
  }
})
