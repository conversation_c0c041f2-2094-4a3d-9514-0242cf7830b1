优化实名认证页的物业员工认证流程
首先优化一下页面
1.输入框采用白底灰色边框,灰底容易让人认为是不可用的
2.实名认证页面动态修改navigationBarTitle.当是实名认证页面时,标题为“实名认证”,当为物业员工认证时,标题为“物业员工认证”.
// 动态设置导航栏标题
wx.setNavigationBarTitle({
  title: '动态设置的标题'
});


页面中的getOrgList用于获取部门的列表
入参:
params:{
   communityId: wx.getStorageSync('selectedCommunity').id,
     
       "orgName": "区域或公司搜索关键字",
}
返回响应:
{
  "errorMessage": null,
  "code": 0,
  "data": [
    {
      "id": "7091961583521234952",
      "orgName": "网信科技公司",
      "type": "company",
      "sort": 0,
      "createTime": "2025-05-26 15:04:06",
      "updateTime": "2025-06-20 08:36:39",
      "parentId": "7091961583521234951",
      "ancestors": null,
      "children": [
        {
          "id": "7091961583521234954",
          "orgName": "研发部",
          "type": "dept",
          "sort": 0,
          "createTime": "2025-06-20 08:36:27",
          "updateTime": null,
          "parentId": "7091961583521234952",
          "ancestors": null,
          "children": null
        },
        {
          "id": "7091961583521234955",
          "orgName": "销售部",
          "type": "dept",
          "sort": 0,
          "createTime": "2025-06-20 08:36:53",
          "updateTime": null,
          "parentId": "7091961583521234952",
          "ancestors": null,
          "children": null
        },
        {
          "id": "7091961583521234956",
          "orgName": "会计部",
          "type": "dept",
          "sort": 0,
          "createTime": "2025-06-20 08:37:23",
          "updateTime": "2025-06-20 08:45:04",
          "parentId": "7091961583521234952",
          "ancestors": null,
          "children": null
        }
      ]
    },
    {
      "id": "7091961583521234957",
      "orgName": "城投公司",
      "type": "company",
      "sort": 0,
      "createTime": "2025-06-20 08:45:27",
      "updateTime": "2025-06-20 08:45:31",
      "parentId": "7091961583521234951",
      "ancestors": null,
      "children": [
        {
          "id": "7091961583521234958",
          "orgName": "建筑部",
          "type": "dept",
          "sort": 0,
          "createTime": "2025-06-20 08:45:52",
          "updateTime": null,
          "parentId": "7091961583521234957",
          "ancestors": null,
          "children": null
        },
        {
          "id": "7091961583521234959",
          "orgName": "规划发展部",
          "type": "dept",
          "sort": 0,
          "createTime": "2025-06-20 08:46:31",
          "updateTime": null,
          "parentId": "7091961583521234957",
          "ancestors": null,
          "children": null
        },
        {
          "id": "7091961583521234960",
          "orgName": "融资部",
          "type": "dept",
          "sort": 0,
          "createTime": "2025-06-20 08:46:50",
          "updateTime": null,
          "parentId": "7091961583521234957",
          "ancestors": null,
          "children": null
        },
        {
          "id": "7091961583521234961",
          "orgName": "项目管理部",
          "type": "dept",
          "sort": 0,
          "createTime": "2025-06-20 08:47:07",
          "updateTime": null,
          "parentId": "7091961583521234957",
          "ancestors": null,
          "children": null
        },
        {
          "id": "7091961583521234962",
          "orgName": "资产管理部",
          "type": "dept",
          "sort": 0,
          "createTime": "2025-06-20 08:47:23",
          "updateTime": null,
          "parentId": "7091961583521234957",
          "ancestors": null,
          "children": null
        }
      ]
    }
  ]
}

3.如果为物业员工认证时,点击部门选择框,页面中间弹出getOrgList获取的list列表,弹窗顶部,第一行是已选择的部门名称,第二行是关键字搜索框,下面是数据列表,注意弹窗内列表无数据时的提示效果. 数据的type=company表示公司层级,  "type"= "dept"表示部门层级,点击选择列表中的部门后,关闭弹窗.只有选择部门后,页面的部门名称才会改变.

4.点击职位选择框,页面中间弹出职位列表,弹窗顶部,第一行是已选择的职位名称,第二行是关键字搜索框,下面是结果列表,注意列表无数据时的提示效果.点击列表,单选高亮,点击底部确定后,将所选项保存到物业员工的表单数据中,等待提交时,将所选项的id保存到表单数据中.
入参:
{
  "pageNum": 1,
  "pageSize": 10,
  "positionName": ""
}
返回响应:
{
  "errorMessage": null,
  "code": 0,
  "data": {
    "total": 12,
    "list": [
      {
        "id": "14",
        "positionName": "办公室主任",
        "sort": 0,
        "createTime": "2025-06-18 21:35:05",
        "updateTime": null,
        "orgId": "7091961583521234948"
      },
      {
        "id": "13",
        "positionName": "行政助理",
        "sort": 0,
        "createTime": "2025-06-18 21:34:57",
        "updateTime": null,
        "orgId": "7091961583521234948"
      },
      {
        "id": "12",
        "positionName": "行政主管",
        "sort": 0,
        "createTime": "2025-06-18 21:34:51",
        "updateTime": null,
        "orgId": "7091961583521234948"
      },
      {
        "id": "11",
        "positionName": "行政经理",
        "sort": 0,
        "createTime": "2025-06-18 21:34:42",
        "updateTime": null,
        "orgId": "7091961583521234948"
      },
      {
        "id": "10",
        "positionName": "总经理助理",
        "sort": 0,
        "createTime": "2025-06-18 21:34:36",
        "updateTime": null,
        "orgId": "7091961583521234948"
      },
      {
        "id": "9",
        "positionName": "董事长",
        "sort": 0,
        "createTime": "2025-06-18 21:34:29",
        "updateTime": null,
        "orgId": "7091961583521234948"
      },
      {
        "id": "8",
        "positionName": "总经理",
        "sort": 0,
        "createTime": "2025-06-18 21:34:14",
        "updateTime": null,
        "orgId": "7091961583521234948"
      },
      {
        "id": "7",
        "positionName": "首席执行官",
        "sort": 0,
        "createTime": "2025-06-18 21:34:09",
        "updateTime": null,
        "orgId": "7091961583521234948"
      },
      {
        "id": "6",
        "positionName": "副总经理",
        "sort": 0,
        "createTime": "2025-06-18 21:34:04",
        "updateTime": null,
        "orgId": "7091961583521234948"
      },
      {
        "id": "5",
        "positionName": "行政总监",
        "sort": 0,
        "createTime": "2025-06-18 21:33:58",
        "updateTime": null,
        "orgId": "7091961583521234948"
      }
    ],
    "pageNum": 1,
    "pageSize": 10,
    "size": 10,
    "startRow": 1,
    "endRow": 10,
    "pages": 2,
    "prePage": 0,
    "nextPage": 2,
    "isFirstPage": true,
    "isLastPage": false,
    "hasPreviousPage": false,
    "hasNextPage": true,
    "navigatePages": 8,
    "navigatepageNums": [
      1,
      2
    ],
    "navigateFirstPage": 1,
    "navigateLastPage": 2
  }
}

5.物业员工认证时,身份证号码上一张加上证件类型选择,证件类型获取字典    const certificateDict = util.getDictByNameEn('certificate_type'),然后"身份证号码"改为"证件号码"

6.提交数据
入参:{

  "personName": "员工姓名",
  "certificateType": "证件类型nameEn",
  "idCard": "证件号码",
  "phone": "手机号码",
  "personNumber": "员工编号",
  "media": "图片上传后接口返回的图片文件地址",
  "orgId": "所选部门id",
  "positionId": "职位id",
}


8注意then的res已经在request方法中处理过data了,直接取res就是data了.code和errorMessage不用处理,已经在request方法中处理过了.

