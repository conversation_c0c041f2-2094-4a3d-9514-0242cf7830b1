# 物业工作台工单管理系统API文档

## 概述

本文档描述了物业工作台中工单管理模块的完整API接口和业务逻辑，供后台开发人员参考实现。

## 基础信息

### 接口基础路径
- 物业端接口：`/manage-api/v1/community/work-order`
- 用户端接口：`/users-api/v1/member/work-order`

### 字典数据

#### 工单状态 (work_order_status)
```json
[
  { "nameEn": "wait_process", "nameCn": "待处理" },
  { "nameEn": "accepted", "nameCn": "已受理" },
  { "nameEn": "processing", "nameCn": "处理中" },
  { "nameEn": "pending", "nameCn": "已挂起" },
  { "nameEn": "completed", "nameCn": "已完成" },
  { "nameEn": "cancel", "nameCn": "已取消" }
]
```

#### 工单类型 (work_order_type)
```json
[
  { "nameEn": "repair", "nameCn": "维修" },
  { "nameEn": "complaint", "nameCn": "投诉" },
  { "nameEn": "suggestion", "nameCn": "建议" },
  { "nameEn": "other", "nameCn": "其他" }
]
```

#### 紧急程度 (work_order_level)
```json
[
  { "nameEn": "low", "nameCn": "低" },
  { "nameEn": "medium", "nameCn": "中" },
  { "nameEn": "high", "nameCn": "高" },
  { "nameEn": "urgent", "nameCn": "紧急" }
]
```

#### 区域类型 (region_type)
```json
[
  { "nameEn": "house", "nameCn": "房屋" },
  { "nameEn": "public_area", "nameCn": "公共区域" }
]
```

#### 人员工作状态 (person_work_status)
```json
[
  { "nameEn": "assigned", "nameCn": "已分配" },
  { "nameEn": "processing", "nameCn": "处理中" },
  { "nameEn": "completed", "nameCn": "已完成" }
]
```

## 1. 工单列表接口

### 1.1 获取工单列表
**接口地址：** `GET /manage-api/v1/community/work-order/page`

**入参：**
```json
{
  "pageNum": 1,           // 页码，默认1
  "pageSize": 10,         // 每页大小，默认10
  "communityId": 123,     // 小区ID，必填
  "status": "wait_process", // 工单状态，可选
  "type": "repair",       // 工单类型，可选
  "keyword": "搜索关键字"   // 搜索关键字，可选
}
```

**返回格式：**
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "list": [
      {
        "id": 1001,
        "type": "repair",
        "status": "wait_process",
        "userDescribe": "水管漏水，需要维修",
        "media": "image1.jpg,image2.jpg",
        "regionType": "house",
        "region": "1栋2单元301",
        "residentName": "张三",
        "residentPhone": "13800138000",
        "residentAddress": "1栋2单元301",
        "phone": "13800138000",
        "communityId": 123,
        "level": "high",
        "createTime": "2024-01-15 10:30:00",
        "updateTime": "2024-01-15 10:30:00",
        "timeLine": [],
        "personList": [],
        "workOrderPersonList": []
      }
    ],
    "total": 50,
    "pageNum": 1,
    "pageSize": 10,
    "pages": 5
  }
}
```

## 2. 创建工单接口

### 2.1 创建工单
**接口地址：** `POST /manage-api/v1/community/work-order`

**入参：**
```json
{
  "type": "repair",              // 工单类型，必填
  "userDescribe": "水管漏水需要维修", // 问题描述，必填
  "media": "image1.jpg,image2.jpg", // 图片，多个用逗号分隔
  "regionType": "house",         // 区域类型，必填
  "region": "1栋2单元301",       // 区域地址，必填
  "residentName": "张三",        // 报修人姓名，必填
  "phone": "13800138000",        // 报修人电话，必填
  "communityId": 123,            // 小区ID，必填
  "level": "high"                // 紧急程度，可选
}
```

**返回格式：**
```json
{
  "code": 200,
  "message": "工单创建成功",
  "data": {
    "id": 1001,
    "status": "wait_process"
  }
}
```

## 3. 工单详情接口

### 3.1 获取工单详情
**接口地址：** `GET /manage-api/v1/community/work-order?id={id}`

**入参：**
- id: 工单ID

**返回字段说明：**
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "id": 1001,                    // 工单ID
    "type": "repair",              // 工单类型
    "status": "processing",        // 工单状态
    "userDescribe": "水管漏水需要维修", // 问题描述
    "media": "image1.jpg,image2.jpg", // 问题图片
    "regionType": "house",         // 区域类型
    "region": "1栋2单元301",       // 区域地址
    "residentName": "张三",        // 报修人姓名
    "residentPhone": "13800138000", // 报修人电话
    "residentAddress": "1栋2单元301", // 报修人地址
    "phone": "13800138000",        // 联系电话
    "communityId": 123,            // 小区ID
    "level": "high",               // 紧急程度
    "createTime": "2024-01-15 10:30:00", // 创建时间
    "updateTime": "2024-01-15 14:20:00", // 更新时间
    "timeLine": [                  // 处理时间线
      {
        "id": 1,
        "workOrderId": 1001,
        "operatorType": "property",
        "operatorId": 101,
        "operatorName": "李维修",
        "action": "accepted",
        "note": "已受理工单，将安排人员处理",
        "createTime": "2024-01-15 11:00:00"
      }
    ],
    "personList": [                // 可分配人员列表
      {
        "id": 101,
        "personName": "李维修",
        "phone": "13900139000",
        "positionName": "维修工"
      }
    ],
    "workOrderPersonList": [       // 已分配人员列表
      {
        "id": 1,
        "workOrderId": 1001,
        "personId": 101,
        "status": "processing",
        "assignTime": "2024-01-15 11:30:00"
      }
    ]
  }
}
```

## 4. 工单处理逻辑

### 4.1 顶部按钮显示逻辑

根据工单状态显示不同的操作按钮，按照工单处理流程逐步说明：

#### 1. 待处理状态 (wait_process)
**工单刚创建，等待物业受理**
- ✅ **受理工单按钮**：点击后工单状态变为"已受理"，可分配处理人员
- ✅ **取消工单按钮**：点击后工单状态变为"已取消"，工单结束
- ❌ 处理工单按钮：不显示（需要先受理）
- ❌ 挂起工单按钮：不显示（还未开始处理）
- ❌ 完成工单按钮：不显示（还未开始处理）

#### 2. 已受理状态 (accepted)
**工单已受理，等待开始处理**
- ❌ 受理工单按钮：不显示（已经受理）
- ✅ **处理工单按钮**：点击后工单状态变为"处理中"，开始实际处理
- ✅ **取消工单按钮**：点击后工单状态变为"已取消"，工单结束
- ❌ 挂起工单按钮：不显示（还未开始处理）
- ❌ 完成工单按钮：不显示（还未开始处理）

#### 3. 处理中状态 (processing)
**工单正在处理中**
- ❌ 受理工单按钮：不显示（已经在处理）
- ❌ 处理工单按钮：不显示（已经在处理中）
- ✅ **取消工单按钮**：点击后工单状态变为"已取消"，工单结束
- ✅ **挂起工单按钮**：点击后工单状态变为"已挂起"，暂停处理
- ✅ **完成工单按钮**：点击后工单状态变为"已完成"，工单结束

#### 4. 已挂起状态 (pending)
**工单处理过程中被挂起，等待恢复处理**
- ❌ 受理工单按钮：不显示（已经受理过）
- ✅ **处理工单按钮**：点击后工单状态变为"处理中"，恢复处理
- ✅ **取消工单按钮**：点击后工单状态变为"已取消"，工单结束
- ❌ 挂起工单按钮：不显示（已经是挂起状态）
- ❌ 完成工单按钮：不显示（需要先恢复处理）

#### 5. 已完成状态 (completed/complete)
**工单处理完成**
- ❌ 受理工单按钮：不显示（工单已结束）
- ❌ 处理工单按钮：不显示（工单已结束）
- ❌ 取消工单按钮：不显示（工单已结束）
- ❌ 挂起工单按钮：不显示（工单已结束）
- ❌ 完成工单按钮：不显示（工单已结束）

#### 6. 已取消状态 (cancelled/cancel)
**工单被取消**
- ❌ 受理工单按钮：不显示（工单已结束）
- ❌ 处理工单按钮：不显示（工单已结束）
- ❌ 取消工单按钮：不显示（工单已结束）
- ❌ 挂起工单按钮：不显示（工单已结束）
- ❌ 完成工单按钮：不显示（工单已结束）

#### 按钮显示规则总结
1. **取消工单按钮**：在所有未结束状态下都显示（wait_process、accepted、processing、pending）
2. **受理工单按钮**：仅在待处理状态显示
3. **处理工单按钮**：在已受理状态和已挂起状态显示
4. **挂起工单按钮**：仅在处理中状态显示
5. **完成工单按钮**：仅在处理中状态显示
6. **已完成和已取消状态**：不显示任何操作按钮

### 4.2 工单操作接口

#### 4.2.1 受理工单
**接口地址：** `PUT /manage-api/v1/community/work-order/accepted`

**入参：**
```json
{
  "id": 1001,                    // 工单ID
  "personIds": [101, 102],       // 分配的人员ID列表
  "note": "已安排维修人员处理"     // 受理备注
}
```

#### 4.2.2 处理工单
**接口地址：** `PUT /manage-api/v1/community/work-order/processing`

**入参：**
```json
{
  "id": 1001,                    // 工单ID
  "note": "开始处理工单",         // 处理备注
  "minute": 60                   // 预计处理时长（分钟）
}
```

#### 4.2.3 挂起工单
**接口地址：** `PUT /manage-api/v1/community/work-order/pending`

**入参：**
```json
{
  "id": 1001,                    // 工单ID
  "note": "等待材料到货",         // 挂起原因
  "media": "image1.jpg"          // 挂起相关图片
}
```

#### 4.2.4 完成工单
**接口地址：** `PUT /manage-api/v1/community/work-order/complete`

**入参：**
```json
{
  "id": 1001,                    // 工单ID
  "note": "维修完成，设备正常",    // 完成备注
  "media": "result1.jpg,result2.jpg", // 完成后的图片
  "minute": 120                  // 实际处理时长（分钟）
}
```

#### 4.2.5 取消工单
**接口地址：** `PUT /manage-api/v1/community/work-order/cancel`

**入参：**
```json
{
  "id": 1001,                    // 工单ID
  "note": "重复工单，已取消"       // 取消原因
}
```

### 4.3 获取可分配人员列表
**接口地址：** `GET /manage-api/v1/community/work-order/person`

**入参：**
```json
{
  "communityId": 123,            // 小区ID
  "type": "repair"               // 工单类型（可选，用于筛选对应技能的人员）
}
```

**返回格式：**
```json
{
  "code": 200,
  "message": "success",
  "data": [
    {
      "id": 101,
      "personName": "李维修",
      "phone": "13900139000",
      "positionName": "维修工",
      "departmentName": "维修部",
      "skills": ["水电维修", "设备维护"]
    }
  ]
}
```

## 5. 处理进度时间线逻辑

### 5.1 时间线数据结构

时间线记录工单的每一个操作步骤：

```json
{
  "timeLine": [
    {
      "id": 1,
      "workOrderId": 1001,
      "operatorType": "resident",    // 操作者类型：resident(住户), property(物业)
      "operatorId": 1001,
      "operatorName": "张三",
      "action": "create",            // 操作类型
      "note": "提交维修工单",
      "media": "problem1.jpg",
      "createTime": "2024-01-15 10:30:00"
    },
    {
      "id": 2,
      "workOrderId": 1001,
      "operatorType": "property",
      "operatorId": 101,
      "operatorName": "李维修",
      "action": "accepted",
      "note": "已受理工单，将安排人员处理",
      "createTime": "2024-01-15 11:00:00"
    },
    {
      "id": 3,
      "workOrderId": 1001,
      "operatorType": "property",
      "operatorId": 101,
      "operatorName": "李维修",
      "action": "processing",
      "note": "开始处理工单",
      "minute": 60,
      "createTime": "2024-01-15 11:30:00"
    },
    {
      "id": 4,
      "workOrderId": 1001,
      "operatorType": "property",
      "operatorId": 101,
      "operatorName": "李维修",
      "action": "completed",
      "note": "维修完成，设备正常运行",
      "media": "result1.jpg,result2.jpg",
      "minute": 120,
      "createTime": "2024-01-15 14:20:00"
    }
  ]
}
```

### 5.2 操作类型说明

| action | 说明 | 操作者 |
|--------|------|--------|
| create | 创建工单 | 住户/物业 |
| accepted | 受理工单 | 物业 |
| processing | 开始处理 | 物业 |
| pending | 挂起工单 | 物业 |
| completed | 完成工单 | 物业 |
| cancel | 取消工单 | 住户/物业 |
| assign | 分配人员 | 物业 |
| record | 处理记录 | 物业 |

### 5.3 时间线显示逻辑

前端根据时间线数据渲染处理进度：

1. **按时间倒序排列**：最新的操作在最上方
2. **不同操作类型使用不同图标和颜色**
3. **显示操作者信息**：姓名、角色
4. **显示操作时间**：格式化为易读的时间格式
5. **显示操作备注**：如果有备注则显示
6. **显示相关图片**：如果有图片则可点击查看
7. **显示处理时长**：如果有minute字段则显示预计/实际处理时长

## 6. 统计接口

### 6.1 工单状态统计
**接口地址：** `GET /manage-api/v1/community/work-order/status-count`

**入参：**
```json
{
  "communityId": 123,
  "startTime": "2024-01-01",     // 开始时间（可选）
  "endTime": "2024-01-31"        // 结束时间（可选）
}
```

**返回格式：**
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "total": 100,
    "wait_process": 20,
    "processing": 30,
    "completed": 45,
    "cancel": 5
  }
}
```

### 6.2 工单类型统计
**接口地址：** `GET /manage-api/v1/community/work-order/type-count`

**入参：**
```json
{
  "communityId": 123,
  "startTime": "2024-01-01",
  "endTime": "2024-01-31"
}
```

**返回格式：**
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "total": 100,
    "repair": 60,
    "complaint": 25,
    "suggestion": 10,
    "other": 5
  }
}
```

## 7. 注意事项

1. **权限控制**：物业人员只能操作所属小区的工单
2. **状态流转**：工单状态必须按照规定流程流转，不能跳跃
3. **图片处理**：图片字段为逗号分隔的字符串，需要分割处理
4. **时间格式**：统一使用 `YYYY-MM-DD HH:mm:ss` 格式
5. **分页处理**：列表接口支持分页，前端需要实现上拉加载更多
6. **实时更新**：工单状态变更后需要通知相关人员
7. **数据校验**：所有必填字段都需要进行校验
8. **异常处理**：需要处理网络异常、数据异常等情况
```
